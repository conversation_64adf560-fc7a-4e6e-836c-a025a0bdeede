<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;
use App\Models\Course;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = Campus::all();

        foreach ($campuses as $campus) {
            $courses = $this->getCoursesForCampus($campus->slug);

            foreach ($courses as $courseData) {
                Course::create(array_merge($courseData, [
                    'campus_id' => $campus->id,
                ]));
            }
        }
    }

    private function getCoursesForCampus($campusSlug)
    {
        $courses = [
            'copywriting' => [
                [
                    'title' => 'Copywriting Fundamentals',
                    'slug' => 'copywriting-fundamentals',
                    'description' => 'Master the basics of persuasive writing and learn how to craft compelling copy that converts.',
                    'content' => 'Learn the fundamentals of copywriting including headlines, hooks, and calls to action.',
                    'duration_minutes' => 120,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Email Marketing Mastery',
                    'slug' => 'email-marketing-mastery',
                    'description' => 'Build profitable email campaigns that generate consistent revenue.',
                    'content' => 'Advanced email marketing strategies and automation techniques.',
                    'duration_minutes' => 90,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Sales Page Secrets',
                    'slug' => 'sales-page-secrets',
                    'description' => 'Create high-converting sales pages that turn visitors into customers.',
                    'content' => 'Step-by-step guide to creating sales pages that convert.',
                    'duration_minutes' => 150,
                    'sort_order' => 3,
                    'is_active' => true,
                    'is_premium' => true,
                ],
            ],
            'ecommerce' => [
                [
                    'title' => 'E-Commerce Foundations',
                    'slug' => 'ecommerce-foundations',
                    'description' => 'Learn the basics of starting and running a successful online store.',
                    'content' => 'Everything you need to know to start your e-commerce journey.',
                    'duration_minutes' => 180,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Product Research & Sourcing',
                    'slug' => 'product-research-sourcing',
                    'description' => 'Find winning products and reliable suppliers for your store.',
                    'content' => 'Advanced product research techniques and supplier management.',
                    'duration_minutes' => 120,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Scaling Your Store',
                    'slug' => 'scaling-your-store',
                    'description' => 'Take your e-commerce business from 6 to 7 figures.',
                    'content' => 'Advanced scaling strategies for e-commerce businesses.',
                    'duration_minutes' => 200,
                    'sort_order' => 3,
                    'is_active' => true,
                    'is_premium' => true,
                ],
            ],
            'freelancing' => [
                [
                    'title' => 'Make Your First $100',
                    'slug' => 'make-your-first-100',
                    'description' => 'Step-by-step guide to earning your first $100 online through freelancing.',
                    'content' => 'Practical steps to start freelancing and get your first clients.',
                    'duration_minutes' => 90,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Client Acquisition Mastery',
                    'slug' => 'client-acquisition-mastery',
                    'description' => 'Learn how to find and land high-paying freelance clients consistently.',
                    'content' => 'Advanced client acquisition strategies and techniques.',
                    'duration_minutes' => 110,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
            ],
            'cryptocurrency' => [
                [
                    'title' => 'Crypto Trading Basics',
                    'slug' => 'crypto-trading-basics',
                    'description' => 'Learn the fundamentals of cryptocurrency trading and market analysis.',
                    'content' => 'Introduction to crypto markets and basic trading strategies.',
                    'duration_minutes' => 150,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Advanced Trading Strategies',
                    'slug' => 'advanced-trading-strategies',
                    'description' => 'Master advanced crypto trading techniques and risk management.',
                    'content' => 'Advanced trading strategies for experienced traders.',
                    'duration_minutes' => 180,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => true,
                ],
            ],
        ];

        return $courses[$campusSlug] ?? [];
    }
}
