{"name": "illuminate/redis", "description": "The Illuminate Redis package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Redis\\": ""}}, "suggest": {"ext-redis": "Required to use the php<PERSON>is connector (^4.0|^5.0).", "predis/predis": "Required to use the predis connector (^2.0.2)."}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}