@extends('layouts.app')

@section('content')
<style>
    .lesson-container {
        background: #111;
        min-height: calc(100vh - 80px);
        padding: 2rem 0;
    }

    .lesson-header {
        background: linear-gradient(135deg, {{ $lesson->course->campus->color }}22, {{ $lesson->course->campus->color }}11);
        border: 1px solid {{ $lesson->course->campus->color }}44;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .lesson-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: {{ $lesson->course->campus->color }};
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
        color: #ccc;
        flex-wrap: wrap;
    }

    .breadcrumb a {
        color: {{ $lesson->course->campus->color }};
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .lesson-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #fff;
    }

    .lesson-meta {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ccc;
        font-size: 0.9rem;
    }

    .meta-item i {
        color: {{ $lesson->course->campus->color }};
    }

    .lesson-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    .lesson-type-badge.video {
        background: #ff4444;
        color: #fff;
    }

    .lesson-type-badge.text {
        background: #4CAF50;
        color: #fff;
    }

    .lesson-type-badge.quiz {
        background: #2196F3;
        color: #fff;
    }

    .premium-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .lesson-content {
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 3rem;
        margin-bottom: 2rem;
        line-height: 1.8;
        color: #ccc;
    }

    .lesson-content h1, .lesson-content h2, .lesson-content h3 {
        color: #fff;
        margin-bottom: 1rem;
        margin-top: 2rem;
    }

    .lesson-content h1:first-child,
    .lesson-content h2:first-child,
    .lesson-content h3:first-child {
        margin-top: 0;
    }

    .lesson-content p {
        margin-bottom: 1.5rem;
        font-size: 1.1rem;
    }

    .lesson-content ul, .lesson-content ol {
        margin-bottom: 1.5rem;
        padding-left: 2rem;
    }

    .lesson-content li {
        margin-bottom: 0.5rem;
    }

    .video-placeholder {
        background: linear-gradient(135deg, {{ $lesson->course->campus->color }}33, {{ $lesson->course->campus->color }}11);
        border: 2px dashed {{ $lesson->course->campus->color }}66;
        border-radius: 12px;
        padding: 4rem 2rem;
        text-align: center;
        margin-bottom: 2rem;
        color: #ccc;
    }

    .video-placeholder i {
        font-size: 4rem;
        color: {{ $lesson->course->campus->color }};
        margin-bottom: 1rem;
        display: block;
    }

    .lesson-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .nav-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: 2px solid {{ $lesson->course->campus->color }};
        border-radius: 6px;
        background: transparent;
        color: {{ $lesson->course->campus->color }};
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s;
    }

    .nav-button:hover {
        background: {{ $lesson->course->campus->color }};
        color: #000;
    }

    .nav-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .progress-bar {
        background: #333;
        height: 6px;
        border-radius: 3px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        background: {{ $lesson->course->campus->color }};
        height: 100%;
        width: 0%;
        transition: width 0.3s;
    }

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: {{ $lesson->course->campus->color }};
        text-decoration: none;
        margin-bottom: 2rem;
        font-weight: 600;
        transition: color 0.3s;
    }

    .back-button:hover {
        color: #fff;
    }

    @media (max-width: 768px) {
        .lesson-header {
            padding: 1.5rem;
        }
        
        .lesson-title {
            font-size: 2rem;
        }
        
        .lesson-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .lesson-content {
            padding: 2rem 1.5rem;
        }
        
        .lesson-navigation {
            flex-direction: column;
            gap: 1rem;
        }
        
        .nav-button {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<div class="lesson-container">
    <div class="container">
        <a href="{{ route('course.show', $lesson->course) }}" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Back to {{ $lesson->course->title }}
        </a>

        <div class="lesson-header">
            <div class="breadcrumb">
                <a href="{{ route('dashboard') }}">Dashboard</a>
                <i class="fas fa-chevron-right"></i>
                <a href="{{ route('campus.show', $lesson->course->campus) }}">{{ $lesson->course->campus->name }}</a>
                <i class="fas fa-chevron-right"></i>
                <a href="{{ route('course.show', $lesson->course) }}">{{ $lesson->course->title }}</a>
                <i class="fas fa-chevron-right"></i>
                <span>{{ $lesson->title }}</span>
            </div>

            <h1 class="lesson-title">{{ $lesson->title }}</h1>

            <div class="lesson-meta">
                @if($lesson->duration_minutes)
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ $lesson->duration_minutes }} minutes</span>
                    </div>
                @endif
                
                <div class="lesson-type-badge {{ $lesson->type }}">
                    {{ $lesson->type }}
                </div>
                
                @if($lesson->is_premium)
                    <div class="premium-badge">PREMIUM</div>
                @endif
            </div>

            @if($lesson->description)
                <p style="color: #ccc; font-size: 1.1rem; margin-top: 1rem;">{{ $lesson->description }}</p>
            @endif
        </div>

        @if($lesson->type === 'video')
            <div class="video-placeholder">
                <i class="fas fa-play-circle"></i>
                <h3>Video Content</h3>
                <p>This is where the video player would be integrated</p>
                @if($lesson->video_url)
                    <p style="font-size: 0.9rem; margin-top: 1rem;">Video URL: {{ $lesson->video_url }}</p>
                @endif
            </div>
        @endif

        <div class="lesson-content">
            {!! nl2br(e($lesson->content)) !!}
        </div>

        <div class="lesson-navigation">
            <a href="#" class="nav-button" style="opacity: 0.5; pointer-events: none;">
                <i class="fas fa-chevron-left"></i>
                Previous Lesson
            </a>
            
            <div style="text-align: center; color: #ccc;">
                <div>Lesson Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
                <small>Completed</small>
            </div>
            
            <a href="#" class="nav-button" style="opacity: 0.5; pointer-events: none;">
                Next Lesson
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
</div>
@endsection
