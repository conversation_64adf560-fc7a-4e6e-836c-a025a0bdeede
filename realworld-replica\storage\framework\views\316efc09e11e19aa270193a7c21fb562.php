<?php $__env->startPush('styles'); ?>
<style>
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<style>
    .hero {
        background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
        padding: 100px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        animation: fadeInUp 1s ease-out;
    }

    .hero h1 {
        font-size: 4rem;
        font-weight: bold;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero p {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        color: #ccc;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .features {
        padding: 100px 0;
        background: #111;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        margin-top: 3rem;
    }

    .feature-card {
        background: #1a1a1a;
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        border: 1px solid #333;
        transition: transform 0.3s, border-color 0.3s;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #ffd700;
        box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
    }

    .feature-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .feature-card:nth-child(1) { animation-delay: 0.1s; }
    .feature-card:nth-child(2) { animation-delay: 0.2s; }
    .feature-card:nth-child(3) { animation-delay: 0.3s; }
    .feature-card:nth-child(4) { animation-delay: 0.4s; }
    .feature-card:nth-child(5) { animation-delay: 0.5s; }
    .feature-card:nth-child(6) { animation-delay: 0.6s; }

    .feature-icon {
        font-size: 3rem;
        color: #ffd700;
        margin-bottom: 1rem;
    }

    .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #fff;
    }

    .feature-card p {
        color: #ccc;
        line-height: 1.6;
    }

    .stats {
        padding: 80px 0;
        background: #000;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: bold;
        color: #ffd700;
        display: block;
    }

    .stat-label {
        color: #ccc;
        font-size: 1.1rem;
        margin-top: 0.5rem;
    }

    .pricing {
        padding: 100px 0;
        background: #111;
        text-align: center;
    }

    .pricing-card {
        background: #1a1a1a;
        border: 2px solid #333;
        border-radius: 12px;
        padding: 3rem 2rem;
        max-width: 400px;
        margin: 2rem auto;
        position: relative;
        overflow: hidden;
    }

    .pricing-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
    }

    .testimonials {
        padding: 100px 0;
        background: #000;
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .testimonial-card {
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 2rem;
        position: relative;
    }

    .testimonial-quote {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #ccc;
        margin-bottom: 1.5rem;
        font-style: italic;
    }

    .testimonial-author {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .testimonial-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #000;
    }

    .testimonial-info h4 {
        color: #fff;
        margin-bottom: 0.25rem;
    }

    .testimonial-info p {
        color: #888;
        font-size: 0.9rem;
    }

    .countdown {
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: #fff;
        padding: 1rem;
        text-align: center;
        font-weight: bold;
        margin-bottom: 2rem;
        border-radius: 8px;
        animation: pulse 2s infinite;
    }

    .price {
        font-size: 3rem;
        font-weight: bold;
        color: #ffd700;
        margin-bottom: 0.5rem;
    }

    .price-period {
        color: #ccc;
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .features-list {
        list-style: none;
        margin-bottom: 2rem;
    }

    .features-list li {
        padding: 0.5rem 0;
        color: #ccc;
    }

    .features-list li::before {
        content: '✓';
        color: #ffd700;
        font-weight: bold;
        margin-right: 0.5rem;
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .hero p {
            font-size: 1.2rem;
        }
        
        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>THE REAL WORLD</h1>
            <p>The ultimate all-in-one learning platform guiding you from making your first dollar online to scaling into a multi-million dollar business.</p>
            <div class="cta-buttons">
                <a href="<?php echo e(route('register')); ?>" class="btn btn-primary">JOIN THE REAL WORLD</a>
                <a href="#features" class="btn btn-secondary">Learn More</a>
            </div>
        </div>
    </div>
</section>

<section id="features" class="features">
    <div class="container">
        <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem;">Master Multiple Income Streams</h2>
        <p style="text-align: center; color: #ccc; font-size: 1.2rem;">Learn from multimillionaire mentors across various campuses</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-pen-nib"></i>
                </div>
                <h3>Copywriting</h3>
                <p>Master the art of persuasive writing and turn words into wealth with proven copywriting strategies.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3>E-Commerce</h3>
                <p>Build and scale profitable online stores from scratch with step-by-step guidance.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-laptop-code"></i>
                </div>
                <h3>Freelancing</h3>
                <p>Learn how to make your first $100 online through high-value freelancing services.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fab fa-bitcoin"></i>
                </div>
                <h3>Cryptocurrency</h3>
                <p>Navigate the crypto markets and build digital wealth with expert trading strategies.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Stocks & Trading</h3>
                <p>Master traditional markets and investment strategies for long-term wealth building.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3>Affiliate Marketing</h3>
                <p>Generate passive income through strategic affiliate partnerships and marketing.</p>
            </div>
        </div>
    </div>
</section>

<section class="stats">
    <div class="container">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Join 150,000+ Students Worldwide</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number">150K+</span>
                <div class="stat-label">Active Students</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">8</span>
                <div class="stat-label">Income Campuses</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">24/7</span>
                <div class="stat-label">Community Support</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">$1M+</span>
                <div class="stat-label">Student Earnings</div>
            </div>
        </div>
    </div>
</section>

<section class="testimonials">
    <div class="container">
        <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem;">What Our Students Say</h2>
        <p style="text-align: center; color: #ccc; font-size: 1.2rem;">Real results from real students</p>

        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-quote">
                    "I made my first $1,000 online within 30 days of joining The Real World. The copywriting campus completely changed my life!"
                </div>
                <div class="testimonial-author">
                    <div class="testimonial-avatar">JD</div>
                    <div class="testimonial-info">
                        <h4>John Davis</h4>
                        <p>Copywriting Student</p>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-quote">
                    "The e-commerce strategies taught here are incredible. I went from $0 to $10K/month in just 3 months!"
                </div>
                <div class="testimonial-author">
                    <div class="testimonial-avatar">SM</div>
                    <div class="testimonial-info">
                        <h4>Sarah Miller</h4>
                        <p>E-Commerce Student</p>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-quote">
                    "The crypto campus helped me turn $500 into $15,000. The professors know exactly what they're doing."
                </div>
                <div class="testimonial-author">
                    <div class="testimonial-avatar">MJ</div>
                    <div class="testimonial-info">
                        <h4>Mike Johnson</h4>
                        <p>Cryptocurrency Student</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="pricing">
    <div class="container">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Start Your Journey Today</h2>
        <p style="color: #ccc; font-size: 1.2rem; margin-bottom: 2rem;">Get access to all campuses and start building your wealth</p>
        
        <div class="countdown">
            ⚡ LIMITED TIME: Join now and get your first month for just $49!
        </div>

        <div class="pricing-card">
            <div class="price">$49</div>
            <div class="price-period">per month (normally $97)</div>
            
            <ul class="features-list">
                <li>Access to all 8 campuses</li>
                <li>Live daily calls with professors</li>
                <li>24/7 community chat</li>
                <li>Step-by-step courses</li>
                <li>Real-time market updates</li>
                <li>Exclusive networking opportunities</li>
                <li>Mobile app access</li>
                <li>Cancel anytime</li>
            </ul>
            
            <a href="<?php echo e(route('register')); ?>" class="btn btn-primary" style="width: 100%;">JOIN NOW</a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\realworld-replica\resources\views/home.blade.php ENDPATH**/ ?>