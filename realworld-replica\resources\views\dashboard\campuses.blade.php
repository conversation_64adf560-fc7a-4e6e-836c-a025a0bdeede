@extends('layouts.app')

@section('content')
<style>
    .campuses-page {
        background: #111;
        min-height: calc(100vh - 80px);
        padding: 3rem 0;
    }

    .page-header {
        text-align: center;
        margin-bottom: 4rem;
    }

    .page-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: #ccc;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .campuses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .campus-card {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        border: 1px solid #333;
        border-radius: 12px;
        padding: 2.5rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .campus-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--campus-color, #ffd700);
    }

    .campus-card:hover {
        transform: translateY(-8px);
        border-color: var(--campus-color, #ffd700);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }

    .campus-icon {
        font-size: 4rem;
        color: var(--campus-color, #ffd700);
        margin-bottom: 1.5rem;
        display: block;
        text-align: center;
    }

    .campus-name {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #fff;
        text-align: center;
    }

    .campus-description {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 2rem;
        text-align: center;
        flex-grow: 1;
    }

    .campus-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1.5rem;
        border-top: 1px solid #333;
        margin-top: auto;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--campus-color, #ffd700);
        display: block;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        color: #888;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .campus-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
    }

    .campus-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: #ccc;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #4CAF50;
    }

    .premium-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .filter-tabs {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: 0.75rem 1.5rem;
        border: 2px solid #333;
        border-radius: 25px;
        background: transparent;
        color: #ccc;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        font-weight: 600;
    }

    .filter-tab.active,
    .filter-tab:hover {
        border-color: #ffd700;
        color: #ffd700;
        background: rgba(255, 215, 0, 0.1);
    }

    .search-bar {
        max-width: 400px;
        margin: 0 auto 2rem;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #333;
        border-radius: 25px;
        background: #1a1a1a;
        color: #fff;
        font-size: 1rem;
    }

    .search-input:focus {
        outline: none;
        border-color: #ffd700;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .campuses-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-tabs {
            flex-direction: column;
            align-items: center;
        }
        
        .filter-tab {
            width: 200px;
            text-align: center;
        }
    }
</style>

<div class="campuses-page">
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">All Campuses</h1>
            <p class="page-subtitle">
                Choose your path to financial freedom. Each campus is designed to teach you a specific skill that can generate income online.
            </p>
        </div>

        <div class="search-bar">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" placeholder="Search campuses..." id="campusSearch">
        </div>

        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">All Campuses</button>
            <button class="filter-tab" data-filter="free">Free Access</button>
            <button class="filter-tab" data-filter="premium">Premium Only</button>
        </div>

        <div class="campuses-grid" id="campusesGrid">
            @foreach($campuses as $campus)
                <a href="{{ route('campus.show', $campus) }}" 
                   class="campus-card" 
                   style="--campus-color: {{ $campus->color }}"
                   data-premium="{{ $campus->is_premium ? 'true' : 'false' }}"
                   data-name="{{ strtolower($campus->name) }}">
                    
                    <i class="{{ $campus->icon }} campus-icon"></i>
                    <h3 class="campus-name">{{ $campus->name }}</h3>
                    <p class="campus-description">{{ $campus->description }}</p>
                    
                    <div class="campus-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $campus->courses->count() }}</span>
                            <div class="stat-label">Courses</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ rand(500, 5000) }}</span>
                            <div class="stat-label">Students</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ rand(10, 50) }}h</span>
                            <div class="stat-label">Content</div>
                        </div>
                    </div>
                    
                    <div class="campus-meta">
                        <div class="campus-status">
                            <div class="status-dot"></div>
                            <span>Active</span>
                        </div>
                        
                        @if($campus->is_premium)
                            <div class="premium-badge">PREMIUM</div>
                        @endif
                    </div>
                </a>
            @endforeach
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('campusSearch');
        const filterTabs = document.querySelectorAll('.filter-tab');
        const campusCards = document.querySelectorAll('.campus-card');
        
        let currentFilter = 'all';
        
        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterCampuses(searchTerm, currentFilter);
        });
        
        // Filter tabs
        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                currentFilter = this.dataset.filter;
                filterCampuses(searchInput.value.toLowerCase(), currentFilter);
            });
        });
        
        function filterCampuses(searchTerm, filter) {
            campusCards.forEach(card => {
                const name = card.dataset.name;
                const isPremium = card.dataset.premium === 'true';
                
                let showCard = true;
                
                // Apply search filter
                if (searchTerm && !name.includes(searchTerm)) {
                    showCard = false;
                }
                
                // Apply category filter
                if (filter === 'premium' && !isPremium) {
                    showCard = false;
                } else if (filter === 'free' && isPremium) {
                    showCard = false;
                }
                
                card.style.display = showCard ? 'flex' : 'none';
            });
        }
        
        // Add stagger animation to cards
        campusCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
@endpush
