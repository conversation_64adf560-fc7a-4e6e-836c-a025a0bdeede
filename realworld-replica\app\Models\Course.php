<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'title',
        'slug',
        'description',
        'content',
        'thumbnail',
        'duration_minutes',
        'sort_order',
        'is_active',
        'is_premium',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
    ];

    /**
     * Get the campus that owns the course.
     */
    public function campus()
    {
        return $this->belongsTo(Campus::class);
    }

    /**
     * Get the lessons for the course.
     */
    public function lessons()
    {
        return $this->hasMany(Lesson::class)->orderBy('sort_order');
    }

    /**
     * Get active lessons for the course.
     */
    public function activeLessons()
    {
        return $this->hasMany(Lesson::class)->where('is_active', true)->orderBy('sort_order');
    }
}
