<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'is_premium',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
    ];

    /**
     * Get the courses for the campus.
     */
    public function courses()
    {
        return $this->hasMany(Course::class)->orderBy('sort_order');
    }

    /**
     * Get active courses for the campus.
     */
    public function activeCourses()
    {
        return $this->hasMany(Course::class)->where('is_active', true)->orderBy('sort_order');
    }
}
