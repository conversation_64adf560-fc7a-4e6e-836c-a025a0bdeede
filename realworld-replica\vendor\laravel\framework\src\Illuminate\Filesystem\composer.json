{"name": "illuminate/filesystem", "description": "The Illuminate Filesystem package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0", "symfony/finder": "^6.2"}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}, "files": ["functions.php"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-hash": "Required to use the Filesystem class.", "illuminate/http": "Required for handling uploaded files (^7.0).", "league/flysystem": "Required to use the Flysystem local driver (^3.0.16).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.2).", "symfony/mime": "Required to enable support for guessing extensions (^6.2)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}