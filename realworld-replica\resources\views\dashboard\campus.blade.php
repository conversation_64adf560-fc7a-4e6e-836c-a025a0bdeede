@extends('layouts.app')

@section('content')
<style>
    .campus-header {
        background: linear-gradient(135deg, {{ $campus->color }}22, {{ $campus->color }}11);
        border: 1px solid {{ $campus->color }}44;
        border-radius: 12px;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .campus-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: {{ $campus->color }};
    }

    .campus-icon {
        font-size: 4rem;
        color: {{ $campus->color }};
        margin-bottom: 1rem;
        display: block;
    }

    .campus-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #fff;
    }

    .campus-description {
        font-size: 1.2rem;
        color: #ccc;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .courses-section {
        background: #111;
        min-height: calc(100vh - 300px);
        padding: 2rem 0;
    }

    .section-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        color: #fff;
        text-align: center;
    }

    .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .course-card {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        border: 1px solid #333;
        border-radius: 12px;
        padding: 2rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .course-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: {{ $campus->color }};
    }

    .course-card:hover {
        transform: translateY(-5px);
        border-color: {{ $campus->color }};
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .course-thumbnail {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, {{ $campus->color }}33, {{ $campus->color }}11);
        border-radius: 8px;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: {{ $campus->color }};
    }

    .course-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #fff;
    }

    .course-description {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #333;
    }

    .course-duration {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ccc;
        font-size: 0.9rem;
    }

    .premium-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: {{ $campus->color }};
        text-decoration: none;
        margin-bottom: 2rem;
        font-weight: 600;
        transition: color 0.3s;
    }

    .back-button:hover {
        color: #fff;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #ccc;
    }

    .empty-state i {
        font-size: 4rem;
        color: {{ $campus->color }};
        margin-bottom: 1rem;
        display: block;
    }

    @media (max-width: 768px) {
        .campus-header {
            padding: 2rem 1rem;
        }
        
        .campus-title {
            font-size: 2rem;
        }
        
        .courses-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="courses-section">
    <div class="container">
        <a href="{{ route('dashboard') }}" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Back to Dashboard
        </a>

        <div class="campus-header">
            <i class="{{ $campus->icon }} campus-icon"></i>
            <h1 class="campus-title">{{ $campus->name }}</h1>
            <p class="campus-description">{{ $campus->description }}</p>
        </div>

        @if($courses->count() > 0)
            <h2 class="section-title">Available Courses</h2>
            
            <div class="courses-grid">
                @foreach($courses as $course)
                    <a href="{{ route('course.show', $course) }}" class="course-card">
                        <div class="course-thumbnail">
                            <i class="{{ $campus->icon }}"></i>
                        </div>
                        
                        <h3 class="course-title">{{ $course->title }}</h3>
                        <p class="course-description">{{ $course->description }}</p>
                        
                        <div class="course-meta">
                            <div class="course-duration">
                                <i class="fas fa-clock"></i>
                                <span>{{ $course->duration_minutes ? $course->duration_minutes . ' min' : 'Self-paced' }}</span>
                            </div>
                            
                            @if($course->is_premium)
                                <div class="premium-badge">PREMIUM</div>
                            @endif
                        </div>
                    </a>
                @endforeach
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-graduation-cap"></i>
                <h3>Courses Coming Soon</h3>
                <p>We're working hard to bring you amazing courses for this campus. Check back soon!</p>
            </div>
        @endif
    </div>
</div>
@endsection
