<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Main landing page (jointherealworld.com)
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // Dashboard routes (app.jointherealworld.com functionality)
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/campuses', [DashboardController::class, 'campuses'])->name('campuses');
    Route::get('/campus/{campus}', [DashboardController::class, 'campus'])->name('campus.show');
    Route::get('/course/{course}', [DashboardController::class, 'course'])->name('course.show');
    Route::get('/lesson/{lesson}', [DashboardController::class, 'lesson'])->name('lesson.show');
});
