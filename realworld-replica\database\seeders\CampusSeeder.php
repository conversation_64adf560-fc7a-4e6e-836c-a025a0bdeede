<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;

class CampusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = [
            [
                'name' => 'Copywriting',
                'slug' => 'copywriting',
                'description' => 'Master the art of persuasive writing and turn words into wealth.',
                'icon' => 'fas fa-pen-nib',
                'color' => '#FF6B35',
                'sort_order' => 1,
                'is_active' => true,
                'is_premium' => false,
            ],
            [
                'name' => 'E-Commerce',
                'slug' => 'ecommerce',
                'description' => 'Build and scale profitable online stores from scratch.',
                'icon' => 'fas fa-shopping-cart',
                'color' => '#4ECDC4',
                'sort_order' => 2,
                'is_active' => true,
                'is_premium' => false,
            ],
            [
                'name' => 'Freelancing',
                'slug' => 'freelancing',
                'description' => 'Learn how to make your first $100 online through freelancing.',
                'icon' => 'fas fa-laptop-code',
                'color' => '#45B7D1',
                'sort_order' => 3,
                'is_active' => true,
                'is_premium' => false,
            ],
            [
                'name' => 'Affiliate Marketing',
                'slug' => 'affiliate-marketing',
                'description' => 'Generate passive income through strategic affiliate partnerships.',
                'icon' => 'fas fa-handshake',
                'color' => '#96CEB4',
                'sort_order' => 4,
                'is_active' => true,
                'is_premium' => true,
            ],
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'description' => 'Navigate the crypto markets and build digital wealth.',
                'icon' => 'fab fa-bitcoin',
                'color' => '#F7931A',
                'sort_order' => 5,
                'is_active' => true,
                'is_premium' => false,
            ],
            [
                'name' => 'Stocks & Trading',
                'slug' => 'stocks-trading',
                'description' => 'Master traditional markets and investment strategies.',
                'icon' => 'fas fa-chart-line',
                'color' => '#2ECC71',
                'sort_order' => 6,
                'is_active' => true,
                'is_premium' => false,
            ],
            [
                'name' => 'Amazon FBA',
                'slug' => 'amazon-fba',
                'description' => 'Build a profitable Amazon business with FBA strategies.',
                'icon' => 'fab fa-amazon',
                'color' => '#FF9500',
                'sort_order' => 7,
                'is_active' => true,
                'is_premium' => true,
            ],
            [
                'name' => 'Content Creation',
                'slug' => 'content-creation',
                'description' => 'Build your personal brand and monetize your content.',
                'icon' => 'fas fa-video',
                'color' => '#E74C3C',
                'sort_order' => 8,
                'is_active' => true,
                'is_premium' => false,
            ],
        ];

        foreach ($campuses as $campus) {
            Campus::create($campus);
        }
    }
}
