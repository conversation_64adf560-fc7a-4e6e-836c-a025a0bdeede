@extends('layouts.app')

@section('content')
<style>
    .hero {
        background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
        padding: 100px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero h1 {
        font-size: 4rem;
        font-weight: bold;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero p {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        color: #ccc;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .features {
        padding: 100px 0;
        background: #111;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        margin-top: 3rem;
    }

    .feature-card {
        background: #1a1a1a;
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        border: 1px solid #333;
        transition: transform 0.3s, border-color 0.3s;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #ffd700;
    }

    .feature-icon {
        font-size: 3rem;
        color: #ffd700;
        margin-bottom: 1rem;
    }

    .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #fff;
    }

    .feature-card p {
        color: #ccc;
        line-height: 1.6;
    }

    .stats {
        padding: 80px 0;
        background: #000;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: bold;
        color: #ffd700;
        display: block;
    }

    .stat-label {
        color: #ccc;
        font-size: 1.1rem;
        margin-top: 0.5rem;
    }

    .pricing {
        padding: 100px 0;
        background: #111;
        text-align: center;
    }

    .pricing-card {
        background: #1a1a1a;
        border: 2px solid #333;
        border-radius: 12px;
        padding: 3rem 2rem;
        max-width: 400px;
        margin: 2rem auto;
        position: relative;
        overflow: hidden;
    }

    .pricing-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
    }

    .price {
        font-size: 3rem;
        font-weight: bold;
        color: #ffd700;
        margin-bottom: 0.5rem;
    }

    .price-period {
        color: #ccc;
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .features-list {
        list-style: none;
        margin-bottom: 2rem;
    }

    .features-list li {
        padding: 0.5rem 0;
        color: #ccc;
    }

    .features-list li::before {
        content: '✓';
        color: #ffd700;
        font-weight: bold;
        margin-right: 0.5rem;
    }

    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .hero p {
            font-size: 1.2rem;
        }
        
        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>THE REAL WORLD</h1>
            <p>The ultimate all-in-one learning platform guiding you from making your first dollar online to scaling into a multi-million dollar business.</p>
            <div class="cta-buttons">
                <a href="{{ route('register') }}" class="btn btn-primary">JOIN THE REAL WORLD</a>
                <a href="#features" class="btn btn-secondary">Learn More</a>
            </div>
        </div>
    </div>
</section>

<section id="features" class="features">
    <div class="container">
        <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem;">Master Multiple Income Streams</h2>
        <p style="text-align: center; color: #ccc; font-size: 1.2rem;">Learn from multimillionaire mentors across various campuses</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-pen-nib"></i>
                </div>
                <h3>Copywriting</h3>
                <p>Master the art of persuasive writing and turn words into wealth with proven copywriting strategies.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3>E-Commerce</h3>
                <p>Build and scale profitable online stores from scratch with step-by-step guidance.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-laptop-code"></i>
                </div>
                <h3>Freelancing</h3>
                <p>Learn how to make your first $100 online through high-value freelancing services.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fab fa-bitcoin"></i>
                </div>
                <h3>Cryptocurrency</h3>
                <p>Navigate the crypto markets and build digital wealth with expert trading strategies.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Stocks & Trading</h3>
                <p>Master traditional markets and investment strategies for long-term wealth building.</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3>Affiliate Marketing</h3>
                <p>Generate passive income through strategic affiliate partnerships and marketing.</p>
            </div>
        </div>
    </div>
</section>

<section class="stats">
    <div class="container">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Join 150,000+ Students Worldwide</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number">150K+</span>
                <div class="stat-label">Active Students</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">8</span>
                <div class="stat-label">Income Campuses</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">24/7</span>
                <div class="stat-label">Community Support</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">$1M+</span>
                <div class="stat-label">Student Earnings</div>
            </div>
        </div>
    </div>
</section>

<section class="pricing">
    <div class="container">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Start Your Journey Today</h2>
        <p style="color: #ccc; font-size: 1.2rem; margin-bottom: 2rem;">Get access to all campuses and start building your wealth</p>
        
        <div class="pricing-card">
            <div class="price">$49</div>
            <div class="price-period">per month</div>
            
            <ul class="features-list">
                <li>Access to all 8 campuses</li>
                <li>Live daily calls with professors</li>
                <li>24/7 community chat</li>
                <li>Step-by-step courses</li>
                <li>Real-time market updates</li>
                <li>Exclusive networking opportunities</li>
                <li>Mobile app access</li>
                <li>Cancel anytime</li>
            </ul>
            
            <a href="{{ route('register') }}" class="btn btn-primary" style="width: 100%;">JOIN NOW</a>
        </div>
    </div>
</section>
@endsection
