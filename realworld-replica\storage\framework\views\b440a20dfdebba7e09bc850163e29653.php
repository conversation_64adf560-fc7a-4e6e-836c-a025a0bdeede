<?php $__env->startSection('content'); ?>
<style>
    .dashboard {
        background: #111;
        min-height: calc(100vh - 80px);
        padding: 2rem 0;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #333;
    }

    .welcome-text {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        color: #ccc;
        font-size: 1.1rem;
    }

    .campuses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .campus-card {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        border: 1px solid #333;
        border-radius: 12px;
        padding: 2rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .campus-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--campus-color, #ffd700);
    }

    .campus-card:hover {
        transform: translateY(-5px);
        border-color: var(--campus-color, #ffd700);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .campus-icon {
        font-size: 3rem;
        color: var(--campus-color, #ffd700);
        margin-bottom: 1rem;
        display: block;
    }

    .campus-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #fff;
    }

    .campus-description {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .campus-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #333;
    }

    .campus-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #4CAF50;
    }

    .premium-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #ffd700;
        display: block;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #ccc;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #fff;
    }

    @media (max-width: 768px) {
        .dashboard-header {
            padding: 1.5rem;
        }
        
        .welcome-text {
            font-size: 1.5rem;
        }
        
        .campuses-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="dashboard">
    <div class="container">
        <div class="dashboard-header">
            <h1 class="welcome-text">Welcome back, <?php echo e(auth()->user()->name); ?>!</h1>
            <p class="welcome-subtitle">Ready to continue building your empire? Choose a campus to get started.</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number"><?php echo e($campuses->count()); ?></span>
                <div class="stat-label">Available Campuses</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">0</span>
                <div class="stat-label">Courses Completed</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">0</span>
                <div class="stat-label">Hours Learned</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo e(auth()->user()->is_premium ? 'Premium' : 'Basic'); ?></span>
                <div class="stat-label">Membership Status</div>
            </div>
        </div>

        <h2 class="section-title">Choose Your Campus</h2>
        
        <div class="campuses-grid">
            <?php $__currentLoopData = $campuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('campus.show', $campus)); ?>" class="campus-card" style="--campus-color: <?php echo e($campus->color); ?>">
                    <i class="<?php echo e($campus->icon); ?> campus-icon"></i>
                    <h3 class="campus-name"><?php echo e($campus->name); ?></h3>
                    <p class="campus-description"><?php echo e($campus->description); ?></p>
                    
                    <div class="campus-meta">
                        <div class="campus-status">
                            <div class="status-dot"></div>
                            <span style="color: #ccc;">Active</span>
                        </div>
                        
                        <?php if($campus->is_premium): ?>
                            <div class="premium-badge">PREMIUM</div>
                        <?php endif; ?>
                    </div>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\realworld-replica\resources\views/dashboard/index.blade.php ENDPATH**/ ?>