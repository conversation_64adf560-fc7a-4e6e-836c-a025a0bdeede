<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\Lesson;

class LessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::all();

        foreach ($courses as $course) {
            $lessons = $this->getLessonsForCourse($course->slug);

            foreach ($lessons as $lessonData) {
                Lesson::create(array_merge($lessonData, [
                    'course_id' => $course->id,
                ]));
            }
        }
    }

    private function getLessonsForCourse($courseSlug)
    {
        $lessons = [
            'copywriting-fundamentals' => [
                [
                    'title' => 'Introduction to Copywriting',
                    'slug' => 'introduction-to-copywriting',
                    'description' => 'Learn what copywriting is and why it\'s essential for business success.',
                    'content' => 'Welcome to the world of copywriting! In this lesson, you\'ll discover the fundamentals of persuasive writing and how it can transform your business.',
                    'type' => 'video',
                    'duration_minutes' => 15,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Writing Compelling Headlines',
                    'slug' => 'writing-compelling-headlines',
                    'description' => 'Master the art of creating headlines that grab attention and drive action.',
                    'content' => 'Headlines are the gateway to your content. Learn proven formulas and techniques to create headlines that convert.',
                    'type' => 'text',
                    'duration_minutes' => 20,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'The Psychology of Persuasion',
                    'slug' => 'psychology-of-persuasion',
                    'description' => 'Understand the psychological triggers that make people take action.',
                    'content' => 'Dive deep into the psychology behind effective copywriting and learn how to ethically influence your audience.',
                    'type' => 'video',
                    'duration_minutes' => 25,
                    'sort_order' => 3,
                    'is_active' => true,
                    'is_premium' => false,
                ],
            ],
            'email-marketing-mastery' => [
                [
                    'title' => 'Email Marketing Fundamentals',
                    'slug' => 'email-marketing-fundamentals',
                    'description' => 'Learn the basics of building and managing email campaigns.',
                    'content' => 'Start your email marketing journey with the fundamentals of list building and campaign creation.',
                    'type' => 'video',
                    'duration_minutes' => 18,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Building Your Email List',
                    'slug' => 'building-your-email-list',
                    'description' => 'Strategies to grow your email list with high-quality subscribers.',
                    'content' => 'Discover proven methods to build an engaged email list that converts into sales.',
                    'type' => 'text',
                    'duration_minutes' => 22,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
            ],
            'make-your-first-100' => [
                [
                    'title' => 'Getting Started with Freelancing',
                    'slug' => 'getting-started-freelancing',
                    'description' => 'Your first steps into the world of freelancing.',
                    'content' => 'Learn how to set up your freelancing business and find your first clients.',
                    'type' => 'video',
                    'duration_minutes' => 12,
                    'sort_order' => 1,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Finding Your First Client',
                    'slug' => 'finding-your-first-client',
                    'description' => 'Step-by-step guide to landing your first paying client.',
                    'content' => 'Practical strategies to find and secure your first freelancing client within days.',
                    'type' => 'text',
                    'duration_minutes' => 16,
                    'sort_order' => 2,
                    'is_active' => true,
                    'is_premium' => false,
                ],
                [
                    'title' => 'Delivering Quality Work',
                    'slug' => 'delivering-quality-work',
                    'description' => 'How to exceed client expectations and build long-term relationships.',
                    'content' => 'Learn the secrets to delivering exceptional work that keeps clients coming back.',
                    'type' => 'video',
                    'duration_minutes' => 14,
                    'sort_order' => 3,
                    'is_active' => true,
                    'is_premium' => false,
                ],
            ],
        ];

        return $lessons[$courseSlug] ?? [];
    }
}
