@extends('layouts.app')

@section('content')
<style>
    .course-header {
        background: linear-gradient(135deg, {{ $course->campus->color }}22, {{ $course->campus->color }}11);
        border: 1px solid {{ $course->campus->color }}44;
        border-radius: 12px;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .course-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: {{ $course->campus->color }};
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
        font-size: 0.9rem;
        color: #ccc;
    }

    .breadcrumb a {
        color: {{ $course->campus->color }};
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .course-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #fff;
    }

    .course-description {
        font-size: 1.2rem;
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .course-meta {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
        align-items: center;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ccc;
    }

    .meta-item i {
        color: {{ $course->campus->color }};
    }

    .lessons-section {
        background: #111;
        min-height: calc(100vh - 400px);
        padding: 2rem 0;
    }

    .section-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        color: #fff;
    }

    .lessons-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .lesson-card {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        border: 1px solid #333;
        border-radius: 12px;
        padding: 1.5rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .lesson-card:hover {
        transform: translateY(-2px);
        border-color: {{ $course->campus->color }};
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }

    .lesson-number {
        background: {{ $course->campus->color }};
        color: #000;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        flex-shrink: 0;
    }

    .lesson-content {
        flex: 1;
    }

    .lesson-title {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #fff;
    }

    .lesson-description {
        color: #ccc;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .lesson-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-shrink: 0;
    }

    .lesson-duration {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ccc;
        font-size: 0.9rem;
    }

    .lesson-type {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    .lesson-type.video {
        background: #ff4444;
        color: #fff;
    }

    .lesson-type.text {
        background: #4CAF50;
        color: #fff;
    }

    .premium-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #ccc;
    }

    .empty-state i {
        font-size: 4rem;
        color: {{ $course->campus->color }};
        margin-bottom: 1rem;
        display: block;
    }

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: {{ $course->campus->color }};
        text-decoration: none;
        margin-bottom: 2rem;
        font-weight: 600;
        transition: color 0.3s;
    }

    .back-button:hover {
        color: #fff;
    }

    @media (max-width: 768px) {
        .course-header {
            padding: 2rem 1rem;
        }
        
        .course-title {
            font-size: 2rem;
        }
        
        .course-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .lesson-card {
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
        }
        
        .lesson-meta {
            width: 100%;
            justify-content: space-between;
        }
    }
</style>

<div class="lessons-section">
    <div class="container">
        <a href="{{ route('campus.show', $course->campus) }}" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Back to {{ $course->campus->name }}
        </a>

        <div class="course-header">
            <div class="breadcrumb">
                <a href="{{ route('dashboard') }}">Dashboard</a>
                <i class="fas fa-chevron-right"></i>
                <a href="{{ route('campus.show', $course->campus) }}">{{ $course->campus->name }}</a>
                <i class="fas fa-chevron-right"></i>
                <span>{{ $course->title }}</span>
            </div>

            <h1 class="course-title">{{ $course->title }}</h1>
            <p class="course-description">{{ $course->description }}</p>

            <div class="course-meta">
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ $course->duration_minutes ? $course->duration_minutes . ' minutes' : 'Self-paced' }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-graduation-cap"></i>
                    <span>{{ $lessons->count() }} Lessons</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-users"></i>
                    <span>{{ $course->campus->name }} Campus</span>
                </div>
                @if($course->is_premium)
                    <div class="premium-badge">PREMIUM</div>
                @endif
            </div>
        </div>

        @if($lessons->count() > 0)
            <h2 class="section-title">Course Lessons</h2>
            
            <div class="lessons-list">
                @foreach($lessons as $index => $lesson)
                    <a href="{{ route('lesson.show', $lesson) }}" class="lesson-card">
                        <div class="lesson-number">{{ $index + 1 }}</div>
                        
                        <div class="lesson-content">
                            <h3 class="lesson-title">{{ $lesson->title }}</h3>
                            @if($lesson->description)
                                <p class="lesson-description">{{ $lesson->description }}</p>
                            @endif
                        </div>
                        
                        <div class="lesson-meta">
                            @if($lesson->duration_minutes)
                                <div class="lesson-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ $lesson->duration_minutes }} min</span>
                                </div>
                            @endif
                            
                            <div class="lesson-type {{ $lesson->type }}">
                                {{ $lesson->type }}
                            </div>
                            
                            @if($lesson->is_premium)
                                <div class="premium-badge">PREMIUM</div>
                            @endif
                        </div>
                    </a>
                @endforeach
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-book-open"></i>
                <h3>Lessons Coming Soon</h3>
                <p>We're preparing amazing lessons for this course. Check back soon!</p>
            </div>
        @endif
    </div>
</div>
@endsection
