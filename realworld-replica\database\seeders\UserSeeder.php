<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'username' => 'testuser',
            'country' => 'United States',
            'status' => 'active',
            'is_premium' => true,
            'email_verified_at' => now(),
        ]);

        // Create an admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'username' => 'admin',
            'country' => 'United States',
            'status' => 'active',
            'is_premium' => true,
            'email_verified_at' => now(),
        ]);
    }
}
