<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Lesson;

class DashboardController extends Controller
{
    /**
     * Show the main dashboard.
     */
    public function index()
    {
        $campuses = Campus::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('dashboard.index', compact('campuses'));
    }

    /**
     * Show all campuses.
     */
    public function campuses()
    {
        $campuses = Campus::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('dashboard.campuses', compact('campuses'));
    }

    /**
     * Show a specific campus.
     */
    public function campus(Campus $campus)
    {
        $courses = $campus->activeCourses;

        return view('dashboard.campus', compact('campus', 'courses'));
    }

    /**
     * Show a specific course.
     */
    public function course(Course $course)
    {
        $lessons = $course->activeLessons;

        return view('dashboard.course', compact('course', 'lessons'));
    }

    /**
     * Show a specific lesson.
     */
    public function lesson(Lesson $lesson)
    {
        return view('dashboard.lesson', compact('lesson'));
    }
}
